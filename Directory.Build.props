<Project>
  <PropertyGroup>
    <!-- Suppress TypeScript warnings from third-party libraries -->
    <TypeScriptCompileBlocked>true</TypeScriptCompileBlocked>
    <DefaultItemExcludes>$(DefaultItemExcludes);wwwroot\lib\**</DefaultItemExcludes>
    
    <!-- Suppress specific warnings -->
    <WarningsAsErrors />
    <WarningsNotAsErrors />
    <NoWarn>$(NoWarn);TS6385;TS6387;MSB3027</NoWarn>
  </PropertyGroup>
  
  <!-- Exclude third-party JavaScript files from TypeScript compilation -->
  <ItemGroup>
    <TypeScriptCompile Remove="wwwroot/lib/**/*.js" />
    <TypeScriptCompile Remove="wwwroot/lib/**/*.ts" />
    <Content Remove="wwwroot/lib/**/*.js.map" />
  </ItemGroup>
</Project>
