using Daak_Management.Models;
using Daak_Management.Services;
using Microsoft.AspNetCore.Mvc;

namespace Daak_Management.Controllers
{
    public class DaakController : Controller
    {
        private readonly IDaakDataService _dataService;
        private readonly ILogger<DaakController> _logger;

        // For demo purposes, we'll use a fixed department ID
        // In a real application, this would come from user authentication
        private const int CurrentDepartmentId = 2; // IT Department

        public DaakController(IDaakDataService dataService, ILogger<DaakController> logger)
        {
            _dataService = dataService;
            _logger = logger;
        }

        public IActionResult Index(DemandFilterViewModel? filter, string tab = "demands")
        {
            var viewModel = new DaakDashboardViewModel
            {
                Filter = filter ?? new DemandFilterViewModel(),
                Departments = _dataService.GetDepartments(),
                ActiveTab = tab
            };

            // Get sent demands
            var sentDemands = _dataService.GetSentDemands(CurrentDepartmentId);
            viewModel.SentDemands = _dataService.FilterDemands(sentDemands, viewModel.Filter);

            // Get received demands
            var receivedDemands = _dataService.GetReceivedDemands(CurrentDepartmentId);
            viewModel.ReceivedDemands = _dataService.FilterDemands(receivedDemands, viewModel.Filter);

            return View(viewModel);
        }

        [HttpPost]
        public IActionResult FilterDemands(DemandFilterViewModel filter, string tab = "demands")
        {
            return RedirectToAction("Index", new { 
                startDate = filter.StartDate?.ToString("yyyy-MM-dd"),
                endDate = filter.EndDate?.ToString("yyyy-MM-dd"),
                status = filter.Status,
                tab = tab
            });
        }

        [HttpGet]
        public IActionResult GetDemandTimeline(int id)
        {
            var demand = _dataService.GetDemandById(id);
            if (demand == null)
            {
                return NotFound();
            }

            var timeline = _dataService.GetDemandTimeline(id);
            var viewModel = new DemandDetailsViewModel
            {
                Demand = demand,
                Timeline = timeline
            };

            return PartialView("_DemandTimelineModal", viewModel);
        }

        [HttpGet]
        public IActionResult GetCreateDemandModal()
        {
            var viewModel = new CreateDemandViewModel
            {
                Date = DateTime.Today,
                SenderDepartmentId = CurrentDepartmentId,
                Departments = _dataService.GetDepartments()
            };

            return PartialView("_CreateDemandModal", viewModel);
        }

        [HttpPost]
        public IActionResult CreateDemand(CreateDemandViewModel model)
        {
            if (ModelState.IsValid)
            {
                model.SenderDepartmentId = CurrentDepartmentId;
                var success = _dataService.CreateDemand(model);
                
                if (success)
                {
                    TempData["SuccessMessage"] = "Demand created successfully!";
                    return Json(new { success = true, message = "Demand created successfully!" });
                }
                else
                {
                    return Json(new { success = false, message = "Failed to create demand." });
                }
            }

            model.Departments = _dataService.GetDepartments();
            return PartialView("_CreateDemandModal", model);
        }

        [HttpGet]
        public IActionResult GetProcessDemandModal(int id)
        {
            var demand = _dataService.GetDemandById(id);
            if (demand == null)
            {
                return NotFound();
            }

            var viewModel = new ProcessDemandViewModel
            {
                DemandId = id,
                Demand = demand,
                Departments = _dataService.GetDepartments()
            };

            return PartialView("_ProcessDemandModal", viewModel);
        }

        [HttpPost]
        public IActionResult ProcessDemand(ProcessDemandViewModel model)
        {
            if (ModelState.IsValid)
            {
                // Validate action-specific requirements
                if (model.Action == "forward" && !model.ForwardToDepartmentId.HasValue)
                {
                    ModelState.AddModelError("ForwardToDepartmentId", "Please select a department to forward to.");
                }
                else if (model.Action == "reject" && string.IsNullOrWhiteSpace(model.Comment))
                {
                    ModelState.AddModelError("Comment", "Please provide a reason for rejection.");
                }

                if (ModelState.IsValid)
                {
                    var success = _dataService.ProcessDemand(model);
                    
                    if (success)
                    {
                        var actionText = model.Action switch
                        {
                            "approve" => "approved",
                            "reject" => "rejected",
                            "forward" => "forwarded",
                            _ => "processed"
                        };
                        
                        TempData["SuccessMessage"] = $"Demand {actionText} successfully!";
                        return Json(new { success = true, message = $"Demand {actionText} successfully!" });
                    }
                    else
                    {
                        return Json(new { success = false, message = "Failed to process demand." });
                    }
                }
            }

            model.Demand = _dataService.GetDemandById(model.DemandId) ?? new Demand();
            model.Departments = _dataService.GetDepartments();
            return PartialView("_ProcessDemandModal", model);
        }
    }
}
