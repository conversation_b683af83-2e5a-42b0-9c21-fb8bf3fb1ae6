/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-zm2ln75x20] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-zm2ln75x20] {
  color: #0077cc;
}

.btn-primary[b-zm2ln75x20] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-zm2ln75x20], .nav-pills .show > .nav-link[b-zm2ln75x20] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-zm2ln75x20] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-zm2ln75x20] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-zm2ln75x20] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-zm2ln75x20] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-zm2ln75x20] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
