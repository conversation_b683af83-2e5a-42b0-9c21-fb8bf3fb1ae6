using Daak_Management.Models;

namespace Daak_Management.Services
{
    public interface IDaakDataService
    {
        List<Department> GetDepartments();
        List<Demand> GetSentDemands(int currentDepartmentId);
        List<Demand> GetReceivedDemands(int currentDepartmentId);
        List<Demand> FilterDemands(List<Demand> demands, DemandFilterViewModel filter);
        Demand? GetDemandById(int id);
        List<DemandTimeline> GetDemandTimeline(int demandId);
        bool CreateDemand(CreateDemandViewModel model);
        bool ProcessDemand(ProcessDemandViewModel model);
    }

    public class DaakDataService : IDaakDataService
    {
        private static List<Department> _departments = new List<Department>();
        private static List<Demand> _demands = new List<Demand>();
        private static List<DemandTimeline> _timelines = new List<DemandTimeline>();
        private static int _nextDemandId = 1;
        private static int _nextTimelineId = 1;

        static DaakDataService()
        {
            InitializeSampleData();
        }

        private static void InitializeSampleData()
        {
            // Initialize Departments
            _departments = new List<Department>
            {
                new Department { Id = 1, Name = "Human Resources", Code = "HR" },
                new Department { Id = 2, Name = "Information Technology", Code = "IT" },
                new Department { Id = 3, Name = "Finance", Code = "FIN" },
                new Department { Id = 4, Name = "Administration", Code = "ADMIN" },
                new Department { Id = 5, Name = "Legal", Code = "LEGAL" },
                new Department { Id = 6, Name = "Operations", Code = "OPS" }
            };

            // Initialize Sample Demands
            _demands = new List<Demand>
            {
                new Demand
                {
                    Id = 1,
                    Date = DateTime.Today.AddDays(-5),
                    SenderDepartmentId = 2, // IT
                    ReceiverDepartmentId = 1, // HR
                    Subject = "Request for Additional Staff",
                    Content = "We need 2 additional developers for the upcoming project. Please process the hiring request.",
                    Status = DemandStatus.Pending,
                    CreatedAt = DateTime.Today.AddDays(-5),
                    SenderDepartment = _departments.First(d => d.Id == 2),
                    ReceiverDepartment = _departments.First(d => d.Id == 1)
                },
                new Demand
                {
                    Id = 2,
                    Date = DateTime.Today.AddDays(-3),
                    SenderDepartmentId = 3, // Finance
                    ReceiverDepartmentId = 4, // Admin
                    Subject = "Budget Approval Request",
                    Content = "Please approve the budget for Q2 2025 office supplies and equipment.",
                    Status = DemandStatus.Approved,
                    CreatedAt = DateTime.Today.AddDays(-3),
                    UpdatedAt = DateTime.Today.AddDays(-1),
                    SenderDepartment = _departments.First(d => d.Id == 3),
                    ReceiverDepartment = _departments.First(d => d.Id == 4)
                },
                new Demand
                {
                    Id = 3,
                    Date = DateTime.Today.AddDays(-7),
                    SenderDepartmentId = 1, // HR
                    ReceiverDepartmentId = 5, // Legal
                    Subject = "Contract Review Request",
                    Content = "Please review the employment contracts for new hires. Urgent review needed.",
                    Status = DemandStatus.Forwarded,
                    CreatedAt = DateTime.Today.AddDays(-7),
                    UpdatedAt = DateTime.Today.AddDays(-2),
                    SenderDepartment = _departments.First(d => d.Id == 1),
                    ReceiverDepartment = _departments.First(d => d.Id == 5)
                }
            };

            // Initialize Sample Timeline
            _timelines = new List<DemandTimeline>
            {
                new DemandTimeline
                {
                    Id = 1,
                    DemandId = 1,
                    DepartmentId = 2,
                    Status = DemandStatus.Pending,
                    Date = DateTime.Today.AddDays(-5),
                    Department = _departments.First(d => d.Id == 2)
                },
                new DemandTimeline
                {
                    Id = 2,
                    DemandId = 2,
                    DepartmentId = 3,
                    Status = DemandStatus.Pending,
                    Date = DateTime.Today.AddDays(-3),
                    Department = _departments.First(d => d.Id == 3)
                },
                new DemandTimeline
                {
                    Id = 3,
                    DemandId = 2,
                    DepartmentId = 4,
                    Status = DemandStatus.Approved,
                    Date = DateTime.Today.AddDays(-1),
                    Comment = "Budget approved for Q2 2025",
                    Department = _departments.First(d => d.Id == 4)
                },
                new DemandTimeline
                {
                    Id = 4,
                    DemandId = 3,
                    DepartmentId = 1,
                    Status = DemandStatus.Pending,
                    Date = DateTime.Today.AddDays(-7),
                    Department = _departments.First(d => d.Id == 1)
                },
                new DemandTimeline
                {
                    Id = 5,
                    DemandId = 3,
                    DepartmentId = 5,
                    Status = DemandStatus.Forwarded,
                    Date = DateTime.Today.AddDays(-2),
                    Comment = "Forwarded to Legal for contract review",
                    ForwardedToDepartmentId = 6,
                    Department = _departments.First(d => d.Id == 5),
                    ForwardedToDepartment = _departments.First(d => d.Id == 6)
                }
            };

            _nextDemandId = _demands.Max(d => d.Id) + 1;
            _nextTimelineId = _timelines.Max(t => t.Id) + 1;
        }

        public List<Department> GetDepartments()
        {
            return _departments.ToList();
        }

        public List<Demand> GetSentDemands(int currentDepartmentId)
        {
            return _demands.Where(d => d.SenderDepartmentId == currentDepartmentId).ToList();
        }

        public List<Demand> GetReceivedDemands(int currentDepartmentId)
        {
            return _demands.Where(d => d.ReceiverDepartmentId == currentDepartmentId || 
                                     _timelines.Any(t => t.DemandId == d.Id && t.ForwardedToDepartmentId == currentDepartmentId))
                           .ToList();
        }

        public List<Demand> FilterDemands(List<Demand> demands, DemandFilterViewModel filter)
        {
            var filtered = demands.AsQueryable();

            if (filter.StartDate.HasValue)
                filtered = filtered.Where(d => d.Date >= filter.StartDate.Value);

            if (filter.EndDate.HasValue)
                filtered = filtered.Where(d => d.Date <= filter.EndDate.Value);

            if (filter.Status.HasValue)
                filtered = filtered.Where(d => d.Status == filter.Status.Value);

            return filtered.ToList();
        }

        public Demand? GetDemandById(int id)
        {
            return _demands.FirstOrDefault(d => d.Id == id);
        }

        public List<DemandTimeline> GetDemandTimeline(int demandId)
        {
            return _timelines.Where(t => t.DemandId == demandId).OrderBy(t => t.Date).ToList();
        }

        public bool CreateDemand(CreateDemandViewModel model)
        {
            var demand = new Demand
            {
                Id = _nextDemandId++,
                Date = model.Date,
                SenderDepartmentId = model.SenderDepartmentId,
                ReceiverDepartmentId = model.ReceiverDepartmentId,
                Subject = model.Subject,
                Content = model.Content,
                Status = DemandStatus.Pending,
                CreatedAt = DateTime.Now,
                SenderDepartment = _departments.First(d => d.Id == model.SenderDepartmentId),
                ReceiverDepartment = _departments.First(d => d.Id == model.ReceiverDepartmentId)
            };

            _demands.Add(demand);

            // Add initial timeline entry
            var timeline = new DemandTimeline
            {
                Id = _nextTimelineId++,
                DemandId = demand.Id,
                DepartmentId = model.SenderDepartmentId,
                Status = DemandStatus.Pending,
                Date = DateTime.Now,
                Department = _departments.First(d => d.Id == model.SenderDepartmentId)
            };

            _timelines.Add(timeline);
            return true;
        }

        public bool ProcessDemand(ProcessDemandViewModel model)
        {
            var demand = _demands.FirstOrDefault(d => d.Id == model.DemandId);
            if (demand == null) return false;

            var status = model.Action.ToLower() switch
            {
                "approve" => DemandStatus.Approved,
                "reject" => DemandStatus.Rejected,
                "forward" => DemandStatus.Forwarded,
                _ => DemandStatus.Pending
            };

            demand.Status = status;
            demand.UpdatedAt = DateTime.Now;

            // Add timeline entry
            var timeline = new DemandTimeline
            {
                Id = _nextTimelineId++,
                DemandId = demand.Id,
                DepartmentId = demand.ReceiverDepartmentId,
                Status = status,
                Date = DateTime.Now,
                Comment = model.Comment,
                ForwardedToDepartmentId = model.ForwardToDepartmentId,
                Department = _departments.First(d => d.Id == demand.ReceiverDepartmentId)
            };

            if (model.ForwardToDepartmentId.HasValue)
            {
                timeline.ForwardedToDepartment = _departments.First(d => d.Id == model.ForwardToDepartmentId.Value);
                demand.ReceiverDepartmentId = model.ForwardToDepartmentId.Value;
                demand.ReceiverDepartment = timeline.ForwardedToDepartment;
            }

            _timelines.Add(timeline);
            return true;
        }
    }
}
