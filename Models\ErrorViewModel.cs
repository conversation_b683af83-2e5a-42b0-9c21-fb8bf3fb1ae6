namespace Daak_Management.Models
{
    public class ErrorViewModel
    {
        public string? RequestId { get; set; }

        public bool ShowRequestId => !string.IsNullOrEmpty(RequestId);
    }

    public enum DemandStatus
    {
        Pending,
        Approved,
        Rejected,
        Forwarded
    }

    public class Department
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
    }

    public class Demand
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public int SenderDepartmentId { get; set; }
        public int ReceiverDepartmentId { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public DemandStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public Department? SenderDepartment { get; set; }
        public Department? ReceiverDepartment { get; set; }
        public List<DemandTimeline> Timeline { get; set; } = new List<DemandTimeline>();
    }

    public class DemandTimeline
    {
        public int Id { get; set; }
        public int DemandId { get; set; }
        public int DepartmentId { get; set; }
        public DemandStatus Status { get; set; }
        public string? Comment { get; set; }
        public DateTime Date { get; set; }
        public int? ForwardedToDepartmentId { get; set; }

        // Navigation properties
        public Demand? Demand { get; set; }
        public Department? Department { get; set; }
        public Department? ForwardedToDepartment { get; set; }
    }
}
