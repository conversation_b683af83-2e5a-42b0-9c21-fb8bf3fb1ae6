@model CreateDemandViewModel

<div class="modal fade" id="createDemandModal" tabindex="-1" aria-labelledby="createDemandModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createDemandModalLabel">
                    <i class="bi bi-plus-circle"></i> Create New Demand
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createDemandForm" asp-action="CreateDemand" asp-controller="Daak" method="post">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label asp-for="Date" class="form-label"></label>
                            <input asp-for="Date" class="form-control" type="date" required />
                            <span asp-validation-for="Date" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="SenderDepartmentId" class="form-label"></label>
                            <select asp-for="SenderDepartmentId" class="form-select" disabled>
                                @foreach (var dept in Model.Departments)
                                {
                                    <option value="@dept.Id" selected="@(dept.Id == Model.SenderDepartmentId)">
                                        @dept.Name
                                    </option>
                                }
                            </select>
                            <input type="hidden" asp-for="SenderDepartmentId" />
                            <span asp-validation-for="SenderDepartmentId" class="text-danger"></span>
                        </div>
                        <div class="col-12">
                            <label asp-for="ReceiverDepartmentId" class="form-label"></label>
                            <select asp-for="ReceiverDepartmentId" class="form-select" required>
                                <option value="">Select Receiver Department</option>
                                @foreach (var dept in Model.Departments.Where(d => d.Id != Model.SenderDepartmentId))
                                {
                                    <option value="@dept.Id">@dept.Name</option>
                                }
                            </select>
                            <span asp-validation-for="ReceiverDepartmentId" class="text-danger"></span>
                        </div>
                        <div class="col-12">
                            <label asp-for="Subject" class="form-label"></label>
                            <input asp-for="Subject" class="form-control" placeholder="Enter demand subject" required />
                            <span asp-validation-for="Subject" class="text-danger"></span>
                        </div>
                        <div class="col-12">
                            <label asp-for="Content" class="form-label"></label>
                            <textarea asp-for="Content" class="form-control" rows="6" 
                                      placeholder="Enter full demand notice content..." required></textarea>
                            <span asp-validation-for="Content" class="text-danger"></span>
                            <div class="form-text">Maximum 2000 characters</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle"></i> Create Demand
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.getElementById('createDemandForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        
        // Show loading state
        submitButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Creating...';
        submitButton.disabled = true;
        
        fetch(this.action, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.headers.get('content-type')?.includes('application/json')) {
                return response.json();
            } else {
                return response.text().then(html => {
                    // If we get HTML back, it means there were validation errors
                    document.getElementById('modalContainer').innerHTML = html;
                    return { success: false, isHtml: true };
                });
            }
        })
        .then(result => {
            if (result.isHtml) {
                // Modal was replaced with validation errors, no need to do anything
                return;
            }
            
            if (result.success) {
                // Close modal and refresh page
                const modal = bootstrap.Modal.getInstance(document.getElementById('createDemandModal'));
                modal.hide();
                
                // Show success message and refresh
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            } else {
                alert(result.message || 'An error occurred while creating the demand.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while creating the demand.');
        })
        .finally(() => {
            // Restore button state
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        });
    });

    // Character counter for content textarea
    const contentTextarea = document.querySelector('textarea[name="Content"]');
    if (contentTextarea) {
        const maxLength = 2000;
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        counter.style.marginTop = '5px';
        
        function updateCounter() {
            const remaining = maxLength - contentTextarea.value.length;
            counter.textContent = `${contentTextarea.value.length}/${maxLength} characters`;
            counter.className = remaining < 100 ? 'form-text text-end text-warning' : 'form-text text-end text-muted';
        }
        
        contentTextarea.addEventListener('input', updateCounter);
        contentTextarea.parentNode.appendChild(counter);
        updateCounter();
    }
</script>

@await Html.PartialAsync("_ValidationScriptsPartial")
