{"Files": [{"Id": "E:\\Grind\\Asp Office Task\\Daak_Management\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Daak_Management.bundle.scp.css", "PackagePath": "staticwebassets\\Daak_Management.bundle.scp.css"}, {"Id": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.Daak_Management.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.Daak_Management.props", "PackagePath": "build\\Daak_Management.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.Daak_Management.props", "PackagePath": "buildMultiTargeting\\Daak_Management.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.Daak_Management.props", "PackagePath": "buildTransitive\\Daak_Management.props"}], "ElementsToRemove": []}