is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = Daak_Management
build_property.RootNamespace = Daak_Management
build_property.ProjectDir = E:\Grind\Asp Office Task\Daak_Management\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = E:\Grind\Asp Office Task\Daak_Management
build_property._RazorSourceGeneratorDebug = 

[E:/Grind/Asp Office Task/Daak_Management/Views/Daak/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGFha1xJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[E:/Grind/Asp Office Task/Daak_Management/Views/Daak/_CreateDemandModal.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGFha1xfQ3JlYXRlRGVtYW5kTW9kYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[E:/Grind/Asp Office Task/Daak_Management/Views/Daak/_DemandTimelineModal.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGFha1xfRGVtYW5kVGltZWxpbmVNb2RhbC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[E:/Grind/Asp Office Task/Daak_Management/Views/Daak/_ProcessDemandModal.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGFha1xfUHJvY2Vzc0RlbWFuZE1vZGFsLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[E:/Grind/Asp Office Task/Daak_Management/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[E:/Grind/Asp Office Task/Daak_Management/Views/Home/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxQcml2YWN5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[E:/Grind/Asp Office Task/Daak_Management/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXEVycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[E:/Grind/Asp Office Task/Daak_Management/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[E:/Grind/Asp Office Task/Daak_Management/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[E:/Grind/Asp Office Task/Daak_Management/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[E:/Grind/Asp Office Task/Daak_Management/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-zm2ln75x20
