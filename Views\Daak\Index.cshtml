@model DaakDashboardViewModel
@{
    ViewData["Title"] = "Daak Management System";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">Daak Management System</h2>
            
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            <!-- Filter Section -->
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Filters</h5>
                    <form asp-action="FilterDemands" method="post" class="row g-3">
                        <div class="col-md-3">
                            <label asp-for="Filter.StartDate" class="form-label"></label>
                            <input asp-for="Filter.StartDate" class="form-control" type="date" />
                        </div>
                        <div class="col-md-3">
                            <label asp-for="Filter.EndDate" class="form-label"></label>
                            <input asp-for="Filter.EndDate" class="form-control" type="date" />
                        </div>
                        <div class="col-md-3">
                            <label asp-for="Filter.Status" class="form-label"></label>
                            <select asp-for="Filter.Status" class="form-select">
                                <option value="">All Status</option>
                                <option value="Pending">Pending</option>
                                <option value="Approved">Approved</option>
                                <option value="Rejected">Rejected</option>
                                <option value="Forwarded">Forwarded</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <input type="hidden" name="tab" value="@Model.ActiveTab" />
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> View
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tabs Navigation -->
            <ul class="nav nav-tabs" id="daakTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link @(Model.ActiveTab == "demands" ? "active" : "")" 
                            id="demands-tab" 
                            data-bs-toggle="tab" 
                            data-bs-target="#demands" 
                            type="button" 
                            role="tab" 
                            aria-controls="demands" 
                            aria-selected="@(Model.ActiveTab == "demands" ? "true" : "false")">
                        Demands
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link @(Model.ActiveTab == "received" ? "active" : "")" 
                            id="received-tab" 
                            data-bs-toggle="tab" 
                            data-bs-target="#received" 
                            type="button" 
                            role="tab" 
                            aria-controls="received" 
                            aria-selected="@(Model.ActiveTab == "received" ? "true" : "false")">
                        Received Demands
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="daakTabsContent">
                <!-- Demands Tab -->
                <div class="tab-pane fade @(Model.ActiveTab == "demands" ? "show active" : "")" 
                     id="demands" 
                     role="tabpanel" 
                     aria-labelledby="demands-tab">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">My Demands</h5>
                            <button type="button" class="btn btn-success" onclick="openCreateDemandModal()">
                                <i class="bi bi-plus-circle"></i> Create Demand
                            </button>
                        </div>
                        <div class="card-body">
                            @if (Model.SentDemands.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Date</th>
                                                <th>Receiver Department</th>
                                                <th>Subject</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var demand in Model.SentDemands)
                                            {
                                                <tr>
                                                    <td>@demand.Date.ToString("dd/MM/yyyy")</td>
                                                    <td>@demand.ReceiverDepartment?.Name</td>
                                                    <td>@demand.Subject</td>
                                                    <td>
                                                        <span class="badge bg-@(GetStatusBadgeClass(demand.Status))">
                                                            @demand.Status
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <button type="button" 
                                                                class="btn btn-outline-primary btn-sm" 
                                                                onclick="viewDemandTimeline(@demand.Id)"
                                                                title="View Timeline">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="bi bi-inbox display-1 text-muted"></i>
                                    <p class="text-muted mt-2">No demands found</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Received Demands Tab -->
                <div class="tab-pane fade @(Model.ActiveTab == "received" ? "show active" : "")" 
                     id="received" 
                     role="tabpanel" 
                     aria-labelledby="received-tab">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Received Demands</h5>
                        </div>
                        <div class="card-body">
                            @if (Model.ReceivedDemands.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Date</th>
                                                <th>Sender Department</th>
                                                <th>Subject</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var demand in Model.ReceivedDemands)
                                            {
                                                <tr>
                                                    <td>@demand.Date.ToString("dd/MM/yyyy")</td>
                                                    <td>@demand.SenderDepartment?.Name</td>
                                                    <td>@demand.Subject</td>
                                                    <td>
                                                        <span class="badge bg-@(GetStatusBadgeClass(demand.Status))">
                                                            @demand.Status
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <button type="button" 
                                                                class="btn btn-outline-primary btn-sm" 
                                                                onclick="processDemand(@demand.Id)"
                                                                title="Process Demand">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="bi bi-inbox display-1 text-muted"></i>
                                    <p class="text-muted mt-2">No received demands found</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Containers -->
<div id="modalContainer"></div>

@functions {
    string GetStatusBadgeClass(DemandStatus status)
    {
        return status switch
        {
            DemandStatus.Pending => "warning",
            DemandStatus.Approved => "success",
            DemandStatus.Rejected => "danger",
            DemandStatus.Forwarded => "info",
            _ => "secondary"
        };
    }
}

@section Scripts {
    <script>
        // Tab switching with URL update
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
            tabButtons.forEach(button => {
                button.addEventListener('shown.bs.tab', function(e) {
                    const tabId = e.target.getAttribute('data-bs-target').replace('#', '');
                    const url = new URL(window.location);
                    url.searchParams.set('tab', tabId);
                    window.history.replaceState({}, '', url);
                    
                    // Update hidden input in filter form
                    const hiddenInput = document.querySelector('input[name="tab"]');
                    if (hiddenInput) {
                        hiddenInput.value = tabId;
                    }
                });
            });
        });

        function openCreateDemandModal() {
            fetch('@Url.Action("GetCreateDemandModal", "Daak")')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('modalContainer').innerHTML = html;
                    const modal = new bootstrap.Modal(document.getElementById('createDemandModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error loading modal:', error);
                    alert('Error loading create demand form');
                });
        }

        function viewDemandTimeline(demandId) {
            fetch(`@Url.Action("GetDemandTimeline", "Daak")?id=${demandId}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('modalContainer').innerHTML = html;
                    const modal = new bootstrap.Modal(document.getElementById('timelineModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error loading timeline:', error);
                    alert('Error loading demand timeline');
                });
        }

        function processDemand(demandId) {
            fetch(`@Url.Action("GetProcessDemandModal", "Daak")?id=${demandId}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('modalContainer').innerHTML = html;
                    const modal = new bootstrap.Modal(document.getElementById('processDemandModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error loading modal:', error);
                    alert('Error loading process demand form');
                });
        }
    </script>
}
