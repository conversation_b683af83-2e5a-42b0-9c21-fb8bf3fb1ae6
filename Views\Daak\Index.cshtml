@model DaakDashboardViewModel
@{
    ViewData["Title"] = "Daak Management System";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">Daak Management System</h2>
                <div class="d-flex align-items-center gap-3">
                    <button type="button" class="btn btn-outline-info btn-sm" data-bs-toggle="modal" data-bs-target="#helpModal">
                        <i class="bi bi-question-circle"></i> Help
                    </button>
                    <div class="badge bg-primary fs-6">
                        <i class="bi bi-building"></i> IT Department
                    </div>
                </div>
            </div>
            
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-white bg-primary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Sent</h6>
                                    <h3 class="mb-0">@Model.SentDemands.Count</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-send fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Received</h6>
                                    <h3 class="mb-0">@Model.ReceivedDemands.Count</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-inbox fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Pending</h6>
                                    <h3 class="mb-0">@(Model.SentDemands.Count(d => d.Status == DemandStatus.Pending) + Model.ReceivedDemands.Count(d => d.Status == DemandStatus.Pending))</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-clock fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Approved</h6>
                                    <h3 class="mb-0">@(Model.SentDemands.Count(d => d.Status == DemandStatus.Approved) + Model.ReceivedDemands.Count(d => d.Status == DemandStatus.Approved))</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-check-circle fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Section -->
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Filters</h5>
                    <form asp-action="FilterDemands" method="post" class="row g-3">
                        <div class="col-md-3">
                            <label asp-for="Filter.StartDate" class="form-label"></label>
                            <input asp-for="Filter.StartDate" class="form-control" type="date" />
                        </div>
                        <div class="col-md-3">
                            <label asp-for="Filter.EndDate" class="form-label"></label>
                            <input asp-for="Filter.EndDate" class="form-control" type="date" />
                        </div>
                        <div class="col-md-2">
                            <label asp-for="Filter.Status" class="form-label"></label>
                            <select asp-for="Filter.Status" class="form-select">
                                <option value="">All Status</option>
                                <option value="Pending">Pending</option>
                                <option value="Approved">Approved</option>
                                <option value="Rejected">Rejected</option>
                                <option value="Forwarded">Forwarded</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Search</label>
                            <input type="text" class="form-control" placeholder="Search by subject..." id="searchInput" />
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <input type="hidden" name="tab" value="@Model.ActiveTab" />
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-search"></i> Filter
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                <i class="bi bi-x-circle"></i> Clear
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tabs Navigation -->
            <ul class="nav nav-tabs" id="daakTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link @(Model.ActiveTab == "demands" ? "active" : "")" 
                            id="demands-tab" 
                            data-bs-toggle="tab" 
                            data-bs-target="#demands" 
                            type="button" 
                            role="tab" 
                            aria-controls="demands" 
                            aria-selected="@(Model.ActiveTab == "demands" ? "true" : "false")">
                        Demands
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link @(Model.ActiveTab == "received" ? "active" : "")" 
                            id="received-tab" 
                            data-bs-toggle="tab" 
                            data-bs-target="#received" 
                            type="button" 
                            role="tab" 
                            aria-controls="received" 
                            aria-selected="@(Model.ActiveTab == "received" ? "true" : "false")">
                        Received Demands
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="daakTabsContent">
                <!-- Demands Tab -->
                <div class="tab-pane fade @(Model.ActiveTab == "demands" ? "show active" : "")" 
                     id="demands" 
                     role="tabpanel" 
                     aria-labelledby="demands-tab">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">My Demands</h5>
                            <button type="button" class="btn btn-success" onclick="openCreateDemandModal()">
                                <i class="bi bi-plus-circle"></i> Create Demand
                            </button>
                        </div>
                        <div class="card-body">
                            @if (Model.SentDemands.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Date</th>
                                                <th>Receiver Department</th>
                                                <th>Subject</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var demand in Model.SentDemands)
                                            {
                                                <tr>
                                                    <td>@demand.Date.ToString("dd/MM/yyyy")</td>
                                                    <td>@demand.ReceiverDepartment?.Name</td>
                                                    <td>@demand.Subject</td>
                                                    <td>
                                                        <span class="badge bg-@(GetStatusBadgeClass(demand.Status))">
                                                            @demand.Status
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <button type="button" 
                                                                class="btn btn-outline-primary btn-sm" 
                                                                onclick="viewDemandTimeline(@demand.Id)"
                                                                title="View Timeline">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="bi bi-inbox display-1 text-muted"></i>
                                    <p class="text-muted mt-2">No demands found</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Received Demands Tab -->
                <div class="tab-pane fade @(Model.ActiveTab == "received" ? "show active" : "")" 
                     id="received" 
                     role="tabpanel" 
                     aria-labelledby="received-tab">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Received Demands</h5>
                        </div>
                        <div class="card-body">
                            @if (Model.ReceivedDemands.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Date</th>
                                                <th>Sender Department</th>
                                                <th>Subject</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var demand in Model.ReceivedDemands)
                                            {
                                                <tr>
                                                    <td>@demand.Date.ToString("dd/MM/yyyy")</td>
                                                    <td>@demand.SenderDepartment?.Name</td>
                                                    <td>@demand.Subject</td>
                                                    <td>
                                                        <span class="badge bg-@(GetStatusBadgeClass(demand.Status))">
                                                            @demand.Status
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <button type="button" 
                                                                class="btn btn-outline-primary btn-sm" 
                                                                onclick="processDemand(@demand.Id)"
                                                                title="Process Demand">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="bi bi-inbox display-1 text-muted"></i>
                                    <p class="text-muted mt-2">No received demands found</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Containers -->
<div id="modalContainer"></div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1" aria-labelledby="helpModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="helpModalLabel">
                    <i class="bi bi-question-circle"></i> Daak Management System Help
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <h6><i class="bi bi-info-circle text-primary"></i> System Overview</h6>
                        <p>The Daak Management System helps you manage official demands between departments efficiently.</p>

                        <h6 class="mt-4"><i class="bi bi-send text-success"></i> Demands Tab</h6>
                        <ul>
                            <li><strong>Create Demand:</strong> Click the green "Create Demand" button to send a new demand to another department</li>
                            <li><strong>View Timeline:</strong> Click the eye icon to see the journey of your demand through different departments</li>
                            <li><strong>Filter:</strong> Use date range and status filters to find specific demands</li>
                        </ul>

                        <h6 class="mt-4"><i class="bi bi-inbox text-info"></i> Received Demands Tab</h6>
                        <ul>
                            <li><strong>Process Demand:</strong> Click the eye icon to approve, reject, or forward received demands</li>
                            <li><strong>Approve:</strong> Accept the demand (optional comment)</li>
                            <li><strong>Reject:</strong> Decline the demand (comment required)</li>
                            <li><strong>Forward:</strong> Send to another department (select department)</li>
                        </ul>

                        <h6 class="mt-4"><i class="bi bi-search text-warning"></i> Search & Filter</h6>
                        <ul>
                            <li><strong>Date Filter:</strong> Filter demands by date range</li>
                            <li><strong>Status Filter:</strong> Filter by Pending, Approved, Rejected, or Forwarded</li>
                            <li><strong>Search:</strong> Type in the search box to find demands by subject</li>
                            <li><strong>Clear:</strong> Reset all filters and search</li>
                        </ul>

                        <h6 class="mt-4"><i class="bi bi-graph-up text-primary"></i> Dashboard</h6>
                        <p>The summary cards at the top show:</p>
                        <ul>
                            <li><strong>Total Sent:</strong> Number of demands you've sent</li>
                            <li><strong>Total Received:</strong> Number of demands you've received</li>
                            <li><strong>Pending:</strong> Total pending demands (sent + received)</li>
                            <li><strong>Approved:</strong> Total approved demands (sent + received)</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> Close
                </button>
            </div>
        </div>
    </div>
</div>

@functions {
    string GetStatusBadgeClass(DemandStatus status)
    {
        return status switch
        {
            DemandStatus.Pending => "warning",
            DemandStatus.Approved => "success",
            DemandStatus.Rejected => "danger",
            DemandStatus.Forwarded => "info",
            _ => "secondary"
        };
    }
}

@section Scripts {
    <script>
        // Tab switching with URL update
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
            tabButtons.forEach(button => {
                button.addEventListener('shown.bs.tab', function(e) {
                    const tabId = e.target.getAttribute('data-bs-target').replace('#', '');
                    const url = new URL(window.location);
                    url.searchParams.set('tab', tabId);
                    window.history.replaceState({}, '', url);
                    
                    // Update hidden input in filter form
                    const hiddenInput = document.querySelector('input[name="tab"]');
                    if (hiddenInput) {
                        hiddenInput.value = tabId;
                    }
                });
            });
        });

        function openCreateDemandModal() {
            showLoadingSpinner();
            fetch('@Url.Action("GetCreateDemandModal", "Daak")')
                .then(response => {
                    if (!response.ok) throw new Error('Network response was not ok');
                    return response.text();
                })
                .then(html => {
                    document.getElementById('modalContainer').innerHTML = html;
                    const modal = new bootstrap.Modal(document.getElementById('createDemandModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error loading modal:', error);
                    showErrorAlert('Error loading create demand form. Please try again.');
                })
                .finally(() => {
                    hideLoadingSpinner();
                });
        }

        function viewDemandTimeline(demandId) {
            showLoadingSpinner();
            fetch(`@Url.Action("GetDemandTimeline", "Daak")?id=${demandId}`)
                .then(response => {
                    if (!response.ok) throw new Error('Network response was not ok');
                    return response.text();
                })
                .then(html => {
                    document.getElementById('modalContainer').innerHTML = html;
                    const modal = new bootstrap.Modal(document.getElementById('timelineModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error loading timeline:', error);
                    showErrorAlert('Error loading demand timeline. Please try again.');
                })
                .finally(() => {
                    hideLoadingSpinner();
                });
        }

        function processDemand(demandId) {
            showLoadingSpinner();
            fetch(`@Url.Action("GetProcessDemandModal", "Daak")?id=${demandId}`)
                .then(response => {
                    if (!response.ok) throw new Error('Network response was not ok');
                    return response.text();
                })
                .then(html => {
                    document.getElementById('modalContainer').innerHTML = html;
                    const modal = new bootstrap.Modal(document.getElementById('processDemandModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error loading modal:', error);
                    showErrorAlert('Error loading process demand form. Please try again.');
                })
                .finally(() => {
                    hideLoadingSpinner();
                });
        }

        // Utility functions
        function showLoadingSpinner() {
            const spinner = document.createElement('div');
            spinner.id = 'loadingSpinner';
            spinner.className = 'position-fixed top-50 start-50 translate-middle';
            spinner.style.zIndex = '9999';
            spinner.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            `;
            document.body.appendChild(spinner);
        }

        function hideLoadingSpinner() {
            const spinner = document.getElementById('loadingSpinner');
            if (spinner) {
                spinner.remove();
            }
        }

        function showErrorAlert(message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3';
            alertDiv.style.zIndex = '9999';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            document.body.appendChild(alertDiv);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        function clearFilters() {
            document.querySelector('input[name="Filter.StartDate"]').value = '';
            document.querySelector('input[name="Filter.EndDate"]').value = '';
            document.querySelector('select[name="Filter.Status"]').value = '';
            document.getElementById('searchInput').value = '';

            // Submit the form to clear filters
            document.querySelector('form[asp-action="FilterDemands"]').submit();
        }

        // Search functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const activeTab = document.querySelector('.tab-pane.active');
                    const rows = activeTab.querySelectorAll('tbody tr');

                    rows.forEach(row => {
                        const subject = row.cells[2].textContent.toLowerCase(); // Subject column
                        if (subject.includes(searchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            }
        });
    </script>
}
