using System.ComponentModel.DataAnnotations;

namespace Daak_Management.Models
{
    public class DemandFilterViewModel
    {
        [Display(Name = "Start Date")]
        [DataType(DataType.Date)]
        public DateTime? StartDate { get; set; }

        [Display(Name = "End Date")]
        [DataType(DataType.Date)]
        public DateTime? EndDate { get; set; }

        [Display(Name = "Status")]
        public DemandStatus? Status { get; set; }
    }

    public class CreateDemandViewModel
    {
        [Required]
        [Display(Name = "Date")]
        [DataType(DataType.Date)]
        public DateTime Date { get; set; } = DateTime.Today;

        [Required]
        [Display(Name = "Receiver Department")]
        public int ReceiverDepartmentId { get; set; }

        [Required]
        [Display(Name = "Sender Department")]
        public int SenderDepartmentId { get; set; }

        [Required]
        [Display(Name = "Subject")]
        [StringLength(200, ErrorMessage = "Subject cannot exceed 200 characters")]
        public string Subject { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Demand Content")]
        [StringLength(2000, ErrorMessage = "Content cannot exceed 2000 characters")]
        public string Content { get; set; } = string.Empty;

        public List<Department> Departments { get; set; } = new List<Department>();
    }

    public class DemandDetailsViewModel
    {
        public Demand Demand { get; set; } = new Demand();
        public List<DemandTimeline> Timeline { get; set; } = new List<DemandTimeline>();
    }

    public class ProcessDemandViewModel
    {
        public int DemandId { get; set; }
        public Demand Demand { get; set; } = new Demand();
        
        [Display(Name = "Action")]
        public string Action { get; set; } = string.Empty; // "approve", "reject", "forward"

        [Display(Name = "Comment")]
        [StringLength(500, ErrorMessage = "Comment cannot exceed 500 characters")]
        public string? Comment { get; set; }

        [Display(Name = "Forward To Department")]
        public int? ForwardToDepartmentId { get; set; }

        public List<Department> Departments { get; set; } = new List<Department>();
    }

    public class DaakDashboardViewModel
    {
        public DemandFilterViewModel Filter { get; set; } = new DemandFilterViewModel();
        public List<Demand> SentDemands { get; set; } = new List<Demand>();
        public List<Demand> ReceivedDemands { get; set; } = new List<Demand>();
        public List<Department> Departments { get; set; } = new List<Department>();
        public string ActiveTab { get; set; } = "demands"; // "demands" or "received"
    }
}
