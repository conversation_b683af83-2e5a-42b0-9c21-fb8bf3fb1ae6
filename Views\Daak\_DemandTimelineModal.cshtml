@model DemandDetailsViewModel

<div class="modal fade" id="timelineModal" tabindex="-1" aria-labelledby="timelineModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="timelineModalLabel">
                    <i class="bi bi-clock-history"></i> Demand Timeline
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Demand Details -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Demand Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Date:</strong> @Model.Demand.Date.ToString("dd/MM/yyyy")
                            </div>
                            <div class="col-md-6">
                                <strong>Status:</strong> 
                                <span class="badge bg-@(GetStatusBadgeClass(Model.Demand.Status))">
                                    @Model.Demand.Status
                                </span>
                            </div>
                            <div class="col-md-6 mt-2">
                                <strong>From:</strong> @Model.Demand.SenderDepartment?.Name
                            </div>
                            <div class="col-md-6 mt-2">
                                <strong>To:</strong> @Model.Demand.ReceiverDepartment?.Name
                            </div>
                            <div class="col-12 mt-2">
                                <strong>Subject:</strong> @Model.Demand.Subject
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Timeline -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Journey Timeline</h6>
                    </div>
                    <div class="card-body">
                        @if (Model.Timeline.Any())
                        {
                            <div class="timeline">
                                @for (int i = 0; i < Model.Timeline.Count; i++)
                                {
                                    var item = Model.Timeline[i];
                                    var isLast = i == Model.Timeline.Count - 1;
                                    
                                    <div class="timeline-item @(!isLast ? "mb-4" : "")">
                                        <div class="timeline-marker">
                                            <div class="timeline-marker-icon bg-@(GetStatusBadgeClass(item.Status))">
                                                <i class="bi @GetStatusIcon(item.Status)"></i>
                                            </div>
                                            @if (!isLast)
                                            {
                                                <div class="timeline-line"></div>
                                            }
                                        </div>
                                        <div class="timeline-content">
                                            <div class="timeline-header">
                                                <h6 class="timeline-title mb-1">@item.Department?.Name</h6>
                                                <small class="text-muted">@item.Date.ToString("dd/MM/yyyy HH:mm")</small>
                                            </div>
                                            <div class="timeline-body">
                                                <span class="badge bg-@(GetStatusBadgeClass(item.Status)) mb-2">
                                                    @item.Status
                                                </span>
                                                @if (!string.IsNullOrEmpty(item.Comment))
                                                {
                                                    <p class="mb-1"><strong>Comment:</strong> @item.Comment</p>
                                                }
                                                @if (item.ForwardedToDepartment != null)
                                                {
                                                    <p class="mb-0">
                                                        <strong>Forwarded to:</strong> @item.ForwardedToDepartment.Name
                                                    </p>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="bi bi-clock-history display-4 text-muted"></i>
                                <p class="text-muted mt-2">No timeline data available</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> Close
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .timeline {
        position: relative;
    }

    .timeline-item {
        display: flex;
        position: relative;
    }

    .timeline-marker {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 1rem;
    }

    .timeline-marker-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
        z-index: 2;
    }

    .timeline-line {
        width: 2px;
        height: 60px;
        background-color: #dee2e6;
        margin-top: 8px;
    }

    .timeline-content {
        flex: 1;
        padding-top: 8px;
    }

    .timeline-title {
        color: #495057;
        font-weight: 600;
    }

    .timeline-body {
        margin-top: 8px;
    }
</style>

@functions {
    string GetStatusBadgeClass(DemandStatus status)
    {
        return status switch
        {
            DemandStatus.Pending => "warning",
            DemandStatus.Approved => "success",
            DemandStatus.Rejected => "danger",
            DemandStatus.Forwarded => "info",
            _ => "secondary"
        };
    }

    string GetStatusIcon(DemandStatus status)
    {
        return status switch
        {
            DemandStatus.Pending => "bi-clock",
            DemandStatus.Approved => "bi-check-circle",
            DemandStatus.Rejected => "bi-x-circle",
            DemandStatus.Forwarded => "bi-arrow-right-circle",
            _ => "bi-circle"
        };
    }
}
