@model ProcessDemandViewModel

<div class="modal fade" id="processDemandModal" tabindex="-1" aria-labelledby="processDemandModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="processDemandModalLabel">
                    <i class="bi bi-gear"></i> Process Demand
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Demand Details (Read-only) -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Demand Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Date:</strong> @Model.Demand.Date.ToString("dd/MM/yyyy")
                            </div>
                            <div class="col-md-6">
                                <strong>From Department:</strong> @Model.Demand.SenderDepartment?.Name
                            </div>
                            <div class="col-md-6 mt-2">
                                <strong>To Department:</strong> @Model.Demand.ReceiverDepartment?.Name
                            </div>
                            <div class="col-md-6 mt-2">
                                <strong>Current Status:</strong> 
                                <span class="badge bg-@(GetStatusBadgeClass(Model.Demand.Status))">
                                    @Model.Demand.Status
                                </span>
                            </div>
                            <div class="col-12 mt-3">
                                <strong>Subject:</strong>
                                <p class="mb-2">@Model.Demand.Subject</p>
                            </div>
                            <div class="col-12">
                                <strong>Full Demand Content:</strong>
                                <div class="border rounded p-3 bg-light mt-2" style="max-height: 200px; overflow-y: auto;">
                                    @Model.Demand.Content
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Processing Form -->
                <form id="processDemandForm" asp-action="ProcessDemand" asp-controller="Daak" method="post">
                    <input type="hidden" asp-for="DemandId" />
                    
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Process Action</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <!-- Action Selection -->
                                <div class="col-12">
                                    <label class="form-label">Select Action</label>
                                    <div class="btn-group w-100" role="group" aria-label="Action selection">
                                        <input type="radio" class="btn-check" name="Action" id="approve" value="approve" autocomplete="off">
                                        <label class="btn btn-outline-success" for="approve">
                                            <i class="bi bi-check-circle"></i> Approve
                                        </label>

                                        <input type="radio" class="btn-check" name="Action" id="reject" value="reject" autocomplete="off">
                                        <label class="btn btn-outline-danger" for="reject">
                                            <i class="bi bi-x-circle"></i> Reject
                                        </label>

                                        <input type="radio" class="btn-check" name="Action" id="forward" value="forward" autocomplete="off">
                                        <label class="btn btn-outline-info" for="forward">
                                            <i class="bi bi-arrow-right-circle"></i> Forward
                                        </label>
                                    </div>
                                    <span asp-validation-for="Action" class="text-danger"></span>
                                </div>

                                <!-- Comment Box -->
                                <div class="col-12" id="commentSection" style="display: none;">
                                    <label asp-for="Comment" class="form-label">Comment</label>
                                    <textarea asp-for="Comment" class="form-control" rows="3" 
                                              placeholder="Enter your comment..."></textarea>
                                    <span asp-validation-for="Comment" class="text-danger"></span>
                                    <div class="form-text" id="commentHelp"></div>
                                </div>

                                <!-- Forward Department Selection -->
                                <div class="col-12" id="forwardSection" style="display: none;">
                                    <label asp-for="ForwardToDepartmentId" class="form-label">Forward To Department</label>
                                    <select asp-for="ForwardToDepartmentId" class="form-select">
                                        <option value="">Select Department</option>
                                        @foreach (var dept in Model.Departments.Where(d => d.Id != Model.Demand.ReceiverDepartmentId && d.Id != Model.Demand.SenderDepartmentId))
                                        {
                                            <option value="@dept.Id">@dept.Name</option>
                                        }
                                    </select>
                                    <span asp-validation-for="ForwardToDepartmentId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> Cancel
                </button>
                <button type="button" id="submitProcessBtn" class="btn btn-primary" onclick="submitProcessForm()">
                    <i class="bi bi-check-circle"></i> Process Demand
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const actionRadios = document.querySelectorAll('input[name="Action"]');
        const commentSection = document.getElementById('commentSection');
        const forwardSection = document.getElementById('forwardSection');
        const commentHelp = document.getElementById('commentHelp');
        const commentTextarea = document.querySelector('textarea[name="Comment"]');
        const forwardSelect = document.querySelector('select[name="ForwardToDepartmentId"]');

        actionRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                const action = this.value;
                
                // Reset sections
                commentSection.style.display = 'none';
                forwardSection.style.display = 'none';
                commentTextarea.required = false;
                forwardSelect.required = false;
                
                if (action === 'approve') {
                    commentSection.style.display = 'block';
                    commentHelp.textContent = 'Optional: Add approval comments';
                } else if (action === 'reject') {
                    commentSection.style.display = 'block';
                    commentHelp.textContent = 'Required: Please provide reason for rejection';
                    commentTextarea.required = true;
                } else if (action === 'forward') {
                    commentSection.style.display = 'block';
                    forwardSection.style.display = 'block';
                    commentHelp.textContent = 'Optional: Add forwarding comments';
                    forwardSelect.required = true;
                }
            });
        });
    });

    function submitProcessForm() {
        const form = document.getElementById('processDemandForm');
        const formData = new FormData(form);
        const submitButton = document.getElementById('submitProcessBtn');
        const originalText = submitButton.innerHTML;
        
        // Validate action selection
        const selectedAction = document.querySelector('input[name="Action"]:checked');
        if (!selectedAction) {
            alert('Please select an action (Approve, Reject, or Forward)');
            return;
        }

        // Additional validation
        if (selectedAction.value === 'reject' && !formData.get('Comment').trim()) {
            alert('Please provide a reason for rejection');
            return;
        }
        
        if (selectedAction.value === 'forward' && !formData.get('ForwardToDepartmentId')) {
            alert('Please select a department to forward to');
            return;
        }
        
        // Show loading state
        submitButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';
        submitButton.disabled = true;
        
        fetch(form.action, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.headers.get('content-type')?.includes('application/json')) {
                return response.json();
            } else {
                return response.text().then(html => {
                    // If we get HTML back, it means there were validation errors
                    document.getElementById('modalContainer').innerHTML = html;
                    return { success: false, isHtml: true };
                });
            }
        })
        .then(result => {
            if (result.isHtml) {
                // Modal was replaced with validation errors, no need to do anything
                return;
            }
            
            if (result.success) {
                // Close modal and refresh page
                const modal = bootstrap.Modal.getInstance(document.getElementById('processDemandModal'));
                modal.hide();
                
                // Show success message and refresh
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            } else {
                alert(result.message || 'An error occurred while processing the demand.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while processing the demand.');
        })
        .finally(() => {
            // Restore button state
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        });
    }
</script>

@functions {
    string GetStatusBadgeClass(DemandStatus status)
    {
        return status switch
        {
            DemandStatus.Pending => "warning",
            DemandStatus.Approved => "success",
            DemandStatus.Rejected => "danger",
            DemandStatus.Forwarded => "info",
            _ => "secondary"
        };
    }
}
