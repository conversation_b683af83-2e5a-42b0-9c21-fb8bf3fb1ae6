{"Version": 1, "Hash": "LEmhrUznQ5QGvhdrKkzbyb4nskK3I1nXy9fgH+uXpTs=", "Source": "Daak_Management", "BasePath": "_content/Daak_Management", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Daak_Management\\wwwroot", "Source": "Daak_Management", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "Pattern": "**"}], "Assets": [{"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Daak_Management.styles.css", "SourceId": "Daak_Management", "SourceType": "Computed", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/Daak_Management", "RelativePath": "Daak_Management.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Grind\\Asp Office Task\\Daak_Management\\obj\\Debug\\net8.0\\scopedcss\\bundle\\Daak_Management.styles.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Daak_Management.bundle.scp.css", "SourceId": "Daak_Management", "SourceType": "Computed", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/Daak_Management", "RelativePath": "Daak_Management.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Grind\\Asp Office Task\\Daak_Management\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Daak_Management.bundle.scp.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\css\\site.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\favicon.ico", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\js\\site.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/bootstrap/LICENSE", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/jquery-validation/dist/additional-methods.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/jquery-validation/LICENSE.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/jquery/dist/jquery.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/jquery/dist/jquery.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/jquery/dist/jquery.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map"}, {"Identity": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "Daak_Management", "SourceType": "Discovered", "ContentRoot": "E:\\Grind\\Asp Office Task\\Daak_Management\\wwwroot\\", "BasePath": "_content/Daak_Management", "RelativePath": "lib/jquery/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt"}]}