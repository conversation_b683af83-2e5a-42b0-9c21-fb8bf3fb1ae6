html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

/* Daak Management Custom Styles */
.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.table th {
  background-color: #495057;
  color: white;
  font-weight: 600;
  border: none;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.075);
}

.btn-outline-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.25);
}

.badge {
  font-size: 0.75em;
  font-weight: 600;
}

/* Timeline Styles */
.timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  position: relative;
}

.timeline-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 1rem;
}

.timeline-marker-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timeline-line {
  width: 2px;
  height: 60px;
  background-color: #dee2e6;
  margin-top: 8px;
}

.timeline-content {
  flex: 1;
  padding-top: 8px;
}

.timeline-title {
  color: #495057;
  font-weight: 600;
}

.timeline-body {
  margin-top: 8px;
}

/* Modal Enhancements */
.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.modal-title {
  font-weight: 600;
  color: #495057;
}

/* Form Enhancements */
.form-label {
  font-weight: 600;
  color: #495057;
}

.form-control:focus, .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Button Group Enhancements */
.btn-group .btn-check:checked + .btn {
  transform: scale(1.02);
}

/* Status Badge Colors */
.bg-warning {
  background-color: #ffc107 !important;
}

.bg-success {
  background-color: #198754 !important;
}

.bg-danger {
  background-color: #dc3545 !important;
}

.bg-info {
  background-color: #0dcaf0 !important;
}

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.bi-hourglass-split {
  animation: spin 1s linear infinite;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: 0.375rem !important;
    margin-bottom: 0.25rem;
  }

  .card-body h3 {
    font-size: 1.5rem;
  }

  .fs-1 {
    font-size: 2rem !important;
  }

  .modal-dialog {
    margin: 0.5rem;
  }
}

/* Summary Cards */
.card.bg-primary, .card.bg-info, .card.bg-warning, .card.bg-success {
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card.bg-primary:hover, .card.bg-info:hover, .card.bg-warning:hover, .card.bg-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

/* Print Styles */
@media print {
  .btn, .modal, .navbar, .filter-section {
    display: none !important;
  }

  .card {
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }

  .table {
    font-size: 12px;
  }
}