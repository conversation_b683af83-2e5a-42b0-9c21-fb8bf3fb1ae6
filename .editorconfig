# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
indent_style = space
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

# Code files
[*.{cs,csx,vb,vbx}]
indent_size = 4

# XML project files
[*.{csproj,vbproj,vcxproj,vcxproj.filters,proj,projitems,shproj}]
indent_size = 2

# JSON files
[*.json]
indent_size = 2

# JavaScript and TypeScript files
[*.{js,ts}]
indent_size = 2

# CSS and SCSS files
[*.{css,scss}]
indent_size = 2

# HTML files
[*.{html,cshtml}]
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false

# Suppress TypeScript warnings for third-party libraries
[wwwroot/lib/**/*.js]
# Disable TypeScript checking for third-party libraries
typescript.preferences.includePackageJsonAutoImports = off
